import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus, Logger } from '@nestjs/common'
import { Request, Response } from 'express'
import { ValidException } from '../exceptions/valid.exception'
import { BizException } from '../exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import process from 'node:process'
import { hasOwn, isNumber, isObject, isString } from '../utils/misc.util'

let once = false

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(private readonly logger: Logger) {
    this.registerCatchAllExceptionsHook()
  }

  private registerCatchAllExceptionsHook() {
    if (once) {
      return
    }

    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('unhandledRejection:', promise, 'reason:', reason)
    })

    process.on('uncaughtException', (err) => {
      this.logger.error('uncaughtException:', err)
    })

    process.on('SIGTERM', () => {
      this.logger.log('SIGTERM signal received: closing HTTP server')
      process.exit(0)
    })

    once = true
  }

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp()

    const response = ctx.getResponse<Response>()
    const request = ctx.getRequest<Request>()

    if (request.method === 'OPTIONS') {
      return response.status(HttpStatus.OK).send()
    }

    let code: number
    let message: string

    if (exception instanceof BizException) {
      // 处理业务异常
      code = exception.code
      message = exception.message || '业务异常'
    } else if (exception instanceof ValidException) {
      // 处理验证异常
      code = ErrorCodeEnum.VALIDATE_FAILED
      message = (exception.message as string) || '参数校验失败'
    } else {
      // 处理其他异常
      code = this.getErrorCode(exception)
      message = this.getErrorMessage(exception)
    }
    response.status(HttpStatus.OK).type('application/json').send({
      code: code,
      message: message,
    })
  }

  private getErrorCode(exception: unknown): number {
    if (isObject(exception) && hasOwn(exception, 'code')) {
      const code = (exception as { code: unknown }).code
      if (isNumber(code)) {
        return code
      }
    }
    return ErrorCodeEnum.SERVER_BUSY
  }

  private getErrorMessage(exception: unknown): string {
    if (isObject(exception)) {
      if (hasOwn(exception, 'response')) {
        const response = (exception as { response: unknown }).response
        const errorMessage = this.extractStringMessage(response)
        if (errorMessage) return errorMessage
      }

      const directMessage = this.extractStringMessage(exception)
      if (directMessage) return directMessage
    }

    return '服务器繁忙'
  }

  private extractStringMessage(obj: unknown): string | null {
    if (isObject(obj) && hasOwn(obj, 'message')) {
      const msg = (obj as { message: unknown }).message
      return isString(msg) ? msg : null
    }
    return null
  }
}
