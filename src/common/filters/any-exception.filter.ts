import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus, Logger } from '@nestjs/common'
import { Request, Response } from 'express'
import { ValidException } from '../exceptions/valid.exception'
import { BizException } from '../exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import process from 'node:process'


let once = false

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(private readonly logger: Logger) {
    this.registerCatchAllExceptionsHook()
  }

  private registerCatchAllExceptionsHook() {
    if (once) {
      return
    }

    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('unhandledRejection:', promise, 'reason:', reason)
    })

    process.on('uncaughtException', (err) => {
      this.logger.error('uncaughtException:', err)
    })

    process.on('SIGTERM', () => {
      this.logger.log('SIGTERM signal received: closing HTTP server')
      process.exit(0)
    })

    once = true
  }

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp()

    const response = ctx.getResponse<Response>()
    const request = ctx.getRequest<Request>()

    if (request.method === 'OPTIONS') {
      return response.status(HttpStatus.OK).send()
    }

    let code: number
    let message: string

    if (exception instanceof BizException) {
      // 处理业务异常
      code = exception.code
      message = exception.message || '业务异常'
      // 业务异常通常不需要记录到错误日志，只记录到info级别
      this.logger.log(`业务异常: ${message} (code: ${code})`)
    } else if (exception instanceof ValidException) {
      // 处理验证异常
      code = ErrorCodeEnum.VALIDATE_FAILED
      message = (exception.message as string) || '参数校验失败'
      // 验证异常记录到warn级别
      this.logger.warn(`参数校验失败: ${message}`)
    } else {
      // 处理其他异常（系统异常、数据库异常等）
      code = ErrorCodeEnum.SERVER_BUSY
      message = '服务器繁忙'

      // 记录详细的错误信息到日志，但不返回给用户
      this.logger.error('系统异常:', {
        url: request.url,
        method: request.method,
        body: request.body,
        query: request.query,
        params: request.params,
        headers: request.headers,
        exception: exception,
        stack: exception instanceof Error ? exception.stack : undefined,
      })
    }

    response.status(HttpStatus.OK).type('application/json').send({
      code: code,
      message: message,
    })
  }


}
