import { mysqlTable, varchar, int, text, datetime } from 'drizzle-orm/mysql-core'
import { KSUID } from 'ksuid'

/** 视频上传状态枚举 */
export enum VideoUploadStatus {
  /** 上传中 */
  UPLOADING = 0,
  /** 上传成功 */
  SUCCESS = 1,
  /** 上传失败 */
  FAILED = 2,
}

/** 视频上传记录表 */
export const videoUploads = mysqlTable('video_uploads', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),

  /** 原始视频URL */
  originalVideoUrl: varchar('original_video_url', { length: 500 }).notNull(),

  /** 视频标题 */
  title: varchar('title', { length: 200 }).notNull(),

  /** 视频描述 */
  description: text('description'),

  /** 抖音返回的视频ID */
  openVideoId: varchar('open_video_id', { length: 100 }),

  /** 上传状态 */
  status: int('status').default(VideoUploadStatus.UPLOADING),

  /** 上传失败原因 */
  failureReason: text('failure_reason'),

  /** 抖音回调的原始数据 */
  callbackData: text('callback_data'),

  /** 创建者ID */
  creatorId: varchar('creator_id', { length: 100 }).notNull(),

  /** 创建时间 */
  createdAt: datetime('created_at').$default(() => new Date()),

  /** 更新时间 */
  updatedAt: datetime('updated_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()),

  /** 软删除标记 */
  isDeleted: int('is_deleted').default(0),
})

export type VideoUploadModel = typeof videoUploads.$inferSelect
export type VideoUploadInsertType = typeof videoUploads.$inferInsert
