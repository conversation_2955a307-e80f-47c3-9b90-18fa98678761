// Controllers
import { SystemLoginController } from './controllers/login.controller'
import { SystemUserController } from './controllers/user.controller'
import { SystemCategoryController } from './controllers/category.controller'
import { SystemGenresController } from './controllers/genres.controller'
import { SystemConfigController } from './controllers/config.controller'
import { SystemDictController } from './controllers/dict.controller'
import { SystemCommentController } from './controllers/comment.controller'
import { SystemUserInfoController } from './controllers/user-info.controller'
import { DramaController } from './controllers/dramas.controller'
import { EpisodeController } from './controllers/episode.controller'

// Services
import { SystemCategoryService } from './services/category.service'
import { SystemUserService } from './services/user.service'
import { SystemGenresService } from './services/genres.service'
import { SystemConfigService } from './services/config.service'
import { SystemDictService } from './services/dict.service'
import { SystemCommentService } from './services/comment.service'
import { DramaEpisodeService } from './services/drama-episode.service'
import { DramaService } from './services/drama.service'
import { DouyinAuditRecordService } from './services/douyin-audit-record.service'
import { DouyinCallbackService } from './services/douyin-callback.service'

import { Module } from '@nestjs/common'
import { TosModule } from '@/shared/tos/tos.module'

import { DouyinClientProvider } from './providers/douyin-client.provider'
import { DouyinDramaManagementProvider } from './providers/douyin-drama-management.provider'
import { CallBackController } from './controllers/callback.controller'
import { DouyinMediaAssetProvider } from './providers/douyin-media-asset.provider'

@Module({
  imports: [TosModule],
  controllers: [
    SystemUserController,
    SystemLoginController,
    SystemCategoryController,
    SystemGenresController,
    SystemConfigController,
    SystemDictController,

    // 业务相关
    SystemCommentController,
    SystemUserInfoController,
    DramaController,
    EpisodeController,
    CallBackController,
  ],
  providers: [
    SystemUserService,
    SystemCategoryService,
    SystemGenresService,
    SystemConfigService,
    SystemDictService,

    // 业务相关
    SystemCommentService,
    DramaEpisodeService,
    DramaService,
    DouyinAuditRecordService,
    DouyinCallbackService,

    // 第三方接口服务
    DouyinClientProvider,
    DouyinDramaManagementProvider,
    DouyinMediaAssetProvider,
  ],
  exports: [DouyinDramaManagementProvider, DouyinClientProvider, DouyinMediaAssetProvider],
})
export class SystemModule {}
