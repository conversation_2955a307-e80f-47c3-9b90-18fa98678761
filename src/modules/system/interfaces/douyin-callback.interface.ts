/**
 * 抖音回调接口定义
 */

// ====================== 基础回调类型 ======================

/** 回调类型枚举 */
export enum DouyinCallbackTypeEnum {
  /** 视频上传结果回调 */
  UPLOAD_VIDEO = 'upload_video',
  /** 剧审核回调 */
  ALBUM_AUDIT = 'album_audit',
  /** 集审核回调 */
  EPISODE_AUDIT = 'episode_audit',
  /** 后置审核回调 */
  POST_REVIEW = 'post_review',
}

export type DouyinCallbackType = `${DouyinCallbackTypeEnum}`

/** 审核状态枚举 */
export enum DouyinAuditStatus {
  /** 审核未通过 */
  REJECTED = 1,
  /** 审核通过 */
  APPROVED = 2,
}

/** 后置审核结果枚举 */
export enum PostReviewResult {
  /** 审核通过 */
  PASS = 'PASS',
  /** 审核不通过 */
  REJECT = 'REJECT',
}

// ====================== 回调响应格式 ======================

/** 抖音回调响应 */
export interface DouyinCallbackResponse {
  /** 错误码，0表示成功 */
  err_no: number
  /** 错误提示 */
  err_tips: string
}

// ====================== 具体回调数据类型 ======================

/** 视频上传结果回调数据 */
export interface UploadVideoCallbackData {
  /** 小程序ID */
  ma_app_id: string
  /** 视频ID */
  open_video_id: string
  /** 上传结果 */
  success: boolean
  /** 视频同步至抖音云的结果 */
  success_to_dy_cloud?: boolean
  /** 视频的抖音云ID */
  dy_cloud_id?: string
}

/** 剧审核回调数据 */
export interface AlbumAuditCallbackData {
  /** 小程序ID */
  ma_app_id: string
  /** 短剧ID */
  album_id: number
  /** 短剧版本 */
  version: number
  /** 能力列表 */
  scope_list: string[]
  /** 审核状态 */
  audit_status: DouyinAuditStatus
  /** 审核备注 */
  audit_msg?: string
}

/** 集审核回调数据 */
export interface EpisodeAuditCallbackData {
  /** 小程序ID */
  ma_app_id: string
  /** 短剧ID */
  album_id: number
  /** 剧集ID */
  episode_id: number
  /** 能力列表 */
  scope_list: string[]
  /** 版本 */
  version: number
  /** 审核状态 */
  audit_status: DouyinAuditStatus
  /** 审核备注 */
  audit_msg?: string
}

/** 后置审核回调数据 */
export interface PostReviewCallbackData {
  /** 小程序ID */
  app_id: string
  /** 审核ID */
  review_id: string
  /** 审核类型 */
  review_type: string
  /** 剧ID */
  album_id: string
  /** 集ID */
  episode_id?: string
  /** 审核结果 */
  review_result: PostReviewResult
  /** 失败原因 */
  fail_reason?: string
  /** 处理建议 */
  tips?: string
}

// ====================== 联合类型 ======================

/** 所有回调数据的联合类型 */
export type DouyinCallbackData =
  | UploadVideoCallbackData
  | AlbumAuditCallbackData
  | EpisodeAuditCallbackData
  | PostReviewCallbackData

// ====================== 审核记录相关 ======================

/** 创建审核记录请求 */
export interface CreateAuditRecordRequest {
  /** 回调类型 */
  callbackType: DouyinCallbackType
  /** 回调数据 */
  callbackData: DouyinCallbackData
  /** 版本 */
  version: string
}

/** 审核记录响应 */
export interface AuditRecordResponse {
  /** 记录ID */
  id: string
  /** 是否为新记录 */
  isNew: boolean
  /** 审核记录 */
  record: any
}
