import { Injectable, Logger } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { dramas, episodes } from '@/common/drizzle/schema'
import { eq, and } from 'drizzle-orm'
import { LogicDelete } from '@/constants/system.constants'
import {
  DouyinAuditStatus,
  AlbumAuditCallbackData,
  EpisodeAuditCallbackData,
  UploadVideoCallbackData,
  PostReviewCallbackData,
} from '../interfaces/douyin-callback.interface'
import { DouyinAuditRecordService } from './douyin-audit-record.service'

// ====================== 类型定义 ======================

/** 包含短剧信息的剧集数据 */
interface EpisodeWithDrama {
  id: string
  title: string
  drama?: {
    id: string
    title: string
    albumId: string | null
  } | null
}

@Injectable()
export class DouyinCallbackService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly auditRecordService: DouyinAuditRecordService,
    private readonly logger: Logger,
  ) {}

  /**
   * 处理剧审核回调
   */
  async handleAlbumAudit(auditRecord: any) {
    const callbackData = (auditRecord as { record: { rawCallbackData: AlbumAuditCallbackData } }).record.rawCallbackData
    const { album_id, version, audit_status, scope_list, audit_msg } = callbackData

    this.logger.log(`处理剧审核回调: albumId=${album_id}, version=${version}, status=${audit_status}`)

    // 映射审核状态
    const douyinAuditStatus = audit_status === DouyinAuditStatus.APPROVED ? 'approved' : 'rejected'
    const platformAuditStatus = audit_status === DouyinAuditStatus.APPROVED ? 'approved' : 'rejected'

    // 更新短剧状态
    const updateData: {
      version: number
      douyinAuditStatus: string
      platformAuditStatus: string
      auditMsg: string | null
      scopeList: string
      lastAuditTime: Date
      auditSuccessVersion?: number
    } = {
      version: version,
      douyinAuditStatus: douyinAuditStatus,
      platformAuditStatus: platformAuditStatus,
      auditMsg: audit_msg || null,
      scopeList: JSON.stringify(scope_list),
      lastAuditTime: new Date(),
    }

    // 只有审核通过时才更新auditSuccessVersion
    if (douyinAuditStatus === 'approved') {
      updateData.auditSuccessVersion = version
    }

    await this.drizzle.db.update(dramas).set(updateData).where(eq(dramas.albumId, album_id.toString()))

    // 标记记录为已处理
    await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)

    this.logger.log(`剧审核处理完成: albumId=${album_id}, status=${douyinAuditStatus}`)
  }

  /**
   * 处理集审核回调
   */
  async handleEpisodeAudit(auditRecord: any) {
    const callbackData = (auditRecord as { record: { rawCallbackData: EpisodeAuditCallbackData } }).record
      .rawCallbackData
    const { episode_id, version, audit_status } = callbackData
    // 移除未使用的变量: album_id, scope_list, audit_msg

    this.logger.log(`处理集审核回调: episodeId=${episode_id}, version=${version}, status=${audit_status}`)

    // TODO: 实现集审核逻辑
    // 这里需要更新 episodes 表的审核状态
    // 由于当前主要关注剧审核，集审核逻辑可以后续完善

    // 标记记录为已处理
    await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)

    this.logger.log(`集审核处理完成: episodeId=${episode_id}`)
  }

  /**
   * 处理视频上传结果回调
   *
   * 回调说明：
   * - 如果 use_dy_cloud=true，会有两次回调
   * - 第一次：视频上传成功回调 (success=true) 第一次其实没必要处理，但是这里还是打印个日志
   * - 第二次：同步抖音云成功回调 (success_to_dy_cloud=true)
   * - 最终以同步抖音云回调为准
   */
  async handleVideoUpload(auditRecord: any) {
    const callbackData = (auditRecord as { record: { rawCallbackData: UploadVideoCallbackData } }).record
      .rawCallbackData
    const { open_video_id, success, success_to_dy_cloud, dy_cloud_id } = callbackData

    this.logger.log(
      `处理视频上传回调: videoId=${open_video_id}, success=${success}, successToDyCloud=${success_to_dy_cloud}, dyCloudId=${dy_cloud_id}`,
    )

    try {
      // 查找对应的剧集记录
      // 注意：这里需要通过 openVideoId 查找，因为上传时会先获得 open_video_id
      // 额外查询出所属的drama方便后续调试，以及日志打印
      const episode = await this.drizzle.db.query.episodes.findFirst({
        where: and(eq(episodes.openVideoId, open_video_id), eq(episodes.isDeleted, LogicDelete.NotDeleted)),
        with: {
          drama: {
            columns: {
              id: true,
              title: true,
              albumId: true,
            },
          },
        },
      })

      if (!episode) {
        this.logger.warn(`未找到对应的剧集记录: videoId=${open_video_id}`)
        // TODO 这块逻辑待确认 即使找不到记录，也要标记为已处理，避免重复处理
        await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)
        return
      }

      // 判断回调类型和处理逻辑
      if (success_to_dy_cloud !== undefined) {
        // 这是同步抖音云的回调（第二次回调）
        await this.handleDyCloudSyncCallback(episode, success_to_dy_cloud, dy_cloud_id, open_video_id)
      } else if (success !== undefined) {
        // 这是视频上传的回调（第一次回调）
        await this.handleVideoUploadCallback(episode, success, open_video_id)
      }

      // 标记记录为已处理
      await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)

      this.logger.log(`视频上传回调处理完成: videoId=${open_video_id}, episodeId=${episode.id}`)
    } catch (error) {
      this.logger.error(`处理视频上传回调失败: videoId=${open_video_id}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        callbackData,
      })

      // TODO 这块逻辑待确认  即使处理失败，也要标记为已处理，避免无限重试
      await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)
      throw error
    }
  }

  /**
   * 处理视频上传成功回调（第一次回调）
   * 这个方法其实有点多余，但是还是可以保存一下更新记录
   * @private
   */
  private async handleVideoUploadCallback(episode: EpisodeWithDrama, success: boolean, openVideoId: string) {
    this.logger.log(`处理视频上传回调: episodeId=${episode.id}, success=${success}`)

    // 更新视频上传状态
    await this.drizzle.db
      .update(episodes)
      .set({
        videoUploadStatus: success ? 1 : 0, // 1: 上传成功, 0: 上传失败
        updatedAt: new Date(),
      })
      .where(eq(episodes.id, episode.id))

    if (success) {
      this.logger.log(`视频上传成功: episodeId=${episode.id}, videoId=${openVideoId}`)
    } else {
      this.logger.error(`视频上传失败: episodeId=${episode.id}, videoId=${openVideoId}`)
    }
  }

  /**
   * 处理抖音云同步回调（第二次回调）
   * @private
   */
  private async handleDyCloudSyncCallback(
    episode: EpisodeWithDrama,
    successToDyCloud: boolean,
    dyCloudId: string | undefined,
    openVideoId: string,
  ) {
    this.logger.log(
      `处理抖音云同步回调: episodeId=${episode.id}, successToDyCloud=${successToDyCloud}, dyCloudId=${dyCloudId}`,
    )

    if (successToDyCloud && dyCloudId) {
      // 抖音云同步成功，视频可以正常使用，确保视频上传状态为成功
      await this.drizzle.db
        .update(episodes)
        .set({
          videoUploadStatus: 1, // 1: 上传成功（可以正常播放）
          updatedAt: new Date(),
        })
        .where(eq(episodes.id, episode.id))

      this.logger.log(
        `抖音云同步成功，视频可用: episodeId=${episode.id}, videoId=${openVideoId}, dyCloudId=${dyCloudId}`,
      )

      // 记录成功日志，包含关键信息
      this.logger.log(`视频完整上传流程完成`, {
        episodeId: episode.id,
        episodeTitle: episode.title,
        dramaId: episode.drama?.id || null,
        dramaTitle: episode.drama?.title || null,
        albumId: episode.drama?.albumId || null,
        openVideoId,
        dyCloudId,
        operation: 'video_upload_complete',
        timestamp: new Date().toISOString(),
      })
    } else {
      // 抖音云同步失败
      this.logger.error(`抖音云同步失败: episodeId=${episode.id}, videoId=${openVideoId}`)

      // 同步失败，视频不可用，标记为上传失败
      await this.drizzle.db
        .update(episodes)
        .set({
          videoUploadStatus: 0, // 0: 上传失败（同步失败，不可播放）
          updatedAt: new Date(),
        })
        .where(eq(episodes.id, episode.id))

      // 记录详细的失败信息
      this.logger.error(`抖音云同步失败详情`, {
        episodeId: episode.id,
        episodeTitle: episode.title,
        dramaId: episode.drama?.id || null,
        dramaTitle: episode.drama?.title || null,
        albumId: episode.drama?.albumId || null,
        openVideoId,
        dyCloudId,
        operation: 'dy_cloud_sync_failed',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 处理后置审核回调
   */
  async handlePostReview(auditRecord: any) {
    const callbackData = (auditRecord as { record: { rawCallbackData: PostReviewCallbackData } }).record.rawCallbackData
    const { review_id, review_type, review_result } = callbackData

    this.logger.log(`处理后置审核回调: reviewId=${review_id}, type=${review_type}, result=${review_result}`)

    // TODO: 实现后置审核处理逻辑
    // 根据 review_type 和 review_result 更新相应的状态

    // 标记记录为已处理
    await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)

    this.logger.log(`后置审核处理完成: reviewId=${review_id}`)
  }

  /**
   * 批量处理未处理的记录（补偿机制）
   */
  async processUnhandledRecords() {
    this.logger.log('开始处理未处理的审核记录')

    const unprocessedRecords = await this.auditRecordService.findUnprocessedRecords()

    for (const record of unprocessedRecords) {
      // 根据回调类型触发相应的处理
      switch (record.callbackType) {
        case 'album_audit':
          await this.handleAlbumAudit({ id: record.id, record })
          break
        case 'episode_audit':
          await this.handleEpisodeAudit({ id: record.id, record })
          break
        case 'upload_video':
          await this.handleVideoUpload({ id: record.id, record })
          break
        case 'post_review':
          await this.handlePostReview({ id: record.id, record })
          break
      }
    }

    this.logger.log(`未处理记录处理完成，共处理 ${unprocessedRecords.length} 条记录`)
  }
}
