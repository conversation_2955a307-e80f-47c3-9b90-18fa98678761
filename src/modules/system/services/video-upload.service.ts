import { Injectable, Logger } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { videoUploads, VideoUploadStatus } from '@/common/drizzle/schema/video-uploads'
import { getUserContext } from '@/context/user.context'
import { DouyinDramaManagementProvider } from '../providers/douyin-drama-management.provider'
import { VideoUploadRequest } from '../requests/video-upload.request'
import { VideoUploadResponse } from '../responses/video-upload.response'
import { toResponse } from '@/common/utils/transform.util'
import { eq, and } from 'drizzle-orm'
import { LogicDelete } from '@/constants/system.constants'

@Injectable()
export class VideoUploadService {
  private readonly logger = new Logger(VideoUploadService.name)

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly douyinDramaManagementProvider: DouyinDramaManagementProvider,
  ) {}

  /**
   * 上传视频到抖音平台
   * @param request 上传请求
   * @returns 上传记录
   */
  async uploadVideo(request: VideoUploadRequest): Promise<VideoUploadResponse> {
    const user = getUserContext()
    
    this.logger.log(`开始上传视频: ${request.title}, URL: ${request.videoUrl}`)

    // 1. 创建上传记录
    const [uploadId] = await this.drizzle.db
      .insert(videoUploads)
      .values({
        originalVideoUrl: request.videoUrl,
        title: request.title,
        description: request.description,
        status: VideoUploadStatus.UPLOADING,
        creatorId: user.id,
      })
      .$returningId()

    try {
      // 2. 调用抖音上传接口
      const openVideoId = await this.douyinDramaManagementProvider.uploadVideo({
        url: request.videoUrl,
        title: request.title,
        description: request.description,
        format: 'mp4',
        use_dy_cloud: true,
        dy_cloud_id: '',
      })

      // 3. 更新记录为上传中状态（等待回调）
      await this.drizzle.db
        .update(videoUploads)
        .set({
          openVideoId,
          updatedAt: new Date(),
        })
        .where(eq(videoUploads.id, uploadId.id))

      this.logger.log(`视频上传请求成功: uploadId=${uploadId.id}, openVideoId=${openVideoId}`)

      // 4. 查询并返回记录
      const uploadRecord = await this.drizzle.db.query.videoUploads.findFirst({
        where: eq(videoUploads.id, uploadId.id),
      })

      return toResponse(VideoUploadResponse, uploadRecord!)
    } catch (error) {
      // 5. 上传失败，更新状态
      await this.drizzle.db
        .update(videoUploads)
        .set({
          status: VideoUploadStatus.FAILED,
          failureReason: error instanceof Error ? error.message : '上传失败',
          updatedAt: new Date(),
        })
        .where(eq(videoUploads.id, uploadId.id))

      this.logger.error(`视频上传失败: uploadId=${uploadId.id}, error:`, error)
      throw error
    }
  }



  /**
   * 处理抖音视频上传回调
   * @param openVideoId 抖音视频ID
   * @param status 上传状态
   * @param callbackData 回调数据
   */
  async handleVideoUploadCallback(openVideoId: string, status: VideoUploadStatus, callbackData?: any) {
    this.logger.log(`处理视频上传回调: openVideoId=${openVideoId}, status=${status}`)

    await this.drizzle.db
      .update(videoUploads)
      .set({
        status,
        callbackData: callbackData ? JSON.stringify(callbackData) : null,
        updatedAt: new Date(),
      })
      .where(eq(videoUploads.openVideoId, openVideoId))

    this.logger.log(`视频上传状态更新完成: openVideoId=${openVideoId}`)
  }

  /**
   * 查询用户的视频上传记录
   * @param status 状态筛选
   * @returns 上传记录列表
   */
  async getUserVideoUploads(status?: VideoUploadStatus): Promise<VideoUploadResponse[]> {
    const user = getUserContext()
    
    const where = [
      eq(videoUploads.creatorId, user.id),
      eq(videoUploads.isDeleted, LogicDelete.NotDeleted),
    ]

    if (status !== undefined) {
      where.push(eq(videoUploads.status, status))
    }

    const records = await this.drizzle.db.query.videoUploads.findMany({
      where: and(...where),
      orderBy: (table, { desc }) => [desc(table.createdAt)],
    })

    return records.map(record => toResponse(VideoUploadResponse, record))
  }

  /**
   * 根据openVideoId查询上传记录
   * @param openVideoId 抖音视频ID
   * @returns 上传记录
   */
  async getVideoUploadByOpenVideoId(openVideoId: string): Promise<VideoUploadResponse | null> {
    const record = await this.drizzle.db.query.videoUploads.findFirst({
      where: and(
        eq(videoUploads.openVideoId, openVideoId),
        eq(videoUploads.isDeleted, LogicDelete.NotDeleted),
      ),
    })

    return record ? toResponse(VideoUploadResponse, record) : null
  }
}
