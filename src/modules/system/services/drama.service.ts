import { DrizzleService } from '@/common/drizzle/database.provider'
import { dramas, episodes } from '@/common/drizzle/schema'
import { BizException } from '@/common/exceptions/biz.exception'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { pageQuery } from '@/common/utils/page-query.util'
import { toResponse } from '@/common/utils/transform.util'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { LogicDelete } from '@/constants/system.constants'
import { getUserContext, getUserId } from '@/context/user.context'
import { Role } from '@/modules/auth/interfaces/jwt-payload.interface'
import { Injectable, Logger } from '@nestjs/common'
import { SQLWrapper, and, eq, like, count, inArray } from 'drizzle-orm'
import { DouyinDramaManagementProvider } from '../providers/douyin-drama-management.provider'
import {
  DramaAuditStatus,
  FetchAlbumInfo,
  PlayletStatus,
} from '../providers/interfaces/douyin-drama-management.interface'
import { DramaCreateRequest, DramaFilterRequest, DramaUpdateRequest } from '../requests/drama.request'
import { DramaResponse } from '../responses/drama.response'
import { pick } from 'lodash-unified'

@Injectable()
export class DramaService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly logger: Logger,
    private readonly douyinDramaManagementProvider: DouyinDramaManagementProvider,
  ) {}

  // 列表
  async list(filterRequest: DramaFilterRequest) {
    const buildCondition = (filter?: DramaFilterRequest) => {
      const user = getUserContext()

      const wheres: SQLWrapper[] = [eq(dramas.isDeleted, LogicDelete.NotDeleted)]

      if (user.role !== Role.ADMIN) {
        wheres.push(eq(dramas.authorId, user.id))
      }

      if (filter?.title) wheres.push(like(dramas.title, `%${filter.title}%`))
      return and(...wheres)
    }

    const { getData } = pageQuery(this.drizzle.db, dramas, filterRequest, {
      condition: () => buildCondition(filterRequest),
    })

    const { data, total } = await getData()
    const list = toResponse(DramaResponse, data)
    return PaginationResponse.fromPaginationRequest(list, total, filterRequest)
  }

  // 创建短剧
  async create(createRequest: DramaCreateRequest): Promise<string> {
    // 权限校验
    const user = getUserContext()
    if (user.role !== Role.ADMIN) {
      createRequest.authorId = user.id
    }

    // 第一步：上传封面图片到抖音
    const coverUploadTasks = [
      this.douyinDramaManagementProvider.uploadImage(createRequest.coverVertical),
      this.douyinDramaManagementProvider.uploadImage(createRequest.coverHorizontal),
    ]
    const [coverVerticalOpenPicId, coverHorizontalOpenPicId] = await Promise.all(coverUploadTasks)
    this.logger.log(`封面图片上传完成，竖版: ${coverVerticalOpenPicId}, 横版: ${coverHorizontalOpenPicId}`)

    // 上传成本分配图片
    const costDistributionOpenPicId = await this.douyinDramaManagementProvider.uploadImage(
      createRequest.costDistributionUri,
    )

    // 上传承诺书图片（如果有）
    let assuranceOpenPicId: string | undefined
    if (createRequest.assuranceUri) {
      assuranceOpenPicId = await this.douyinDramaManagementProvider.uploadImage(createRequest.assuranceUri)
    }

    // 上传介绍图片到抖音（如果有）
    let introImagesOpenPicIds: string[] = []
    if (createRequest.introImages && createRequest.introImages.length > 0) {
      this.logger.log(`开始上传 ${createRequest.introImages.length} 张介绍图片到抖音`)
      const introUploadTasks = createRequest.introImages.map((imageUrl) =>
        this.douyinDramaManagementProvider.uploadImage(imageUrl),
      )
      introImagesOpenPicIds = await Promise.all(introUploadTasks)
      this.logger.log(`介绍图片上传完成，获得 ${introImagesOpenPicIds.length} 个开放图片ID`)
    }

    // 第二步：构建抖音短剧创建请求
    const albumInfo = {
      title: createRequest.title,
      seq_num: createRequest.seqNum,
      cover_list: [coverVerticalOpenPicId, coverHorizontalOpenPicId],
      year: createRequest.year,
      album_status: createRequest.albumStatus, // 短剧更新状态，用户填
      recommendation: createRequest.recommendation,
      desp: createRequest.desp,
      tag_list: createRequest.tagList.map(String), // 转换为字符串数组
      qualification: 1, // 该字段可先忽略，统一传入1即可。备案信息关注record_audit_info传入即可
      record_audit_info: {
        record_material: {
          name: createRequest.title,
          duration: createRequest.duration,
          seqs_count: createRequest.seqNum,
          production_organisation: createRequest.productionOrganisation,
          director: [createRequest.director],
          producer: [createRequest.producer],
          actor: [createRequest.actor],
          summary: createRequest.summary,
          cost_distribution_uri: costDistributionOpenPicId,
          playlet_production_cost: createRequest.playletProductionCost, // 制作成本
          screen_writer: createRequest.screenWriter ? [createRequest.screenWriter] : undefined,
          ...(assuranceOpenPicId && { assurance_uri: assuranceOpenPicId }), // 只有存在时才添加
        },
        broadcast_record_info:
          createRequest.recordType && createRequest.broadcastRecordNumber
            ? {
                record_type: createRequest.recordType,
                broadcast_record_number: createRequest.broadcastRecordNumber,
              }
            : undefined,
      },
    }

    // 第三步：使用事务确保抖音API调用和数据库保存的一致性
    this.logger.log('开始事务：调用抖音创建短剧接口并保存到本地数据库')

    let douyinResult: string | undefined
    let localId: string

    try {
      // 在事务中执行抖音API调用和数据库保存
      const result = await this.drizzle.db.transaction(async (tx) => {
        // 调用抖音创建短剧接口
        this.logger.log('事务中：调用抖音创建短剧接口')
        const albumId = await this.douyinDramaManagementProvider.createDrama(albumInfo)
        this.logger.log(`事务中：抖音短剧创建成功，获得短剧ID: ${albumId}`)

        // 准备数据库字段
        const fields = pick(createRequest, [
          'title',
          'seqNum',
          'year',
          'albumStatus',
          'recommendation',
          'desp',
          'summary',
          'categoryId',
          'actor',
          'director',
          'duration',
          'productionOrganisation',
          'producer',
          'costDistributionUri',
          'assuranceUri',
          'playletProductionCost',
          'screenWriter',
          'recordType',
          'broadcastRecordNumber',
          'licenseNum',
          'registrationNum',
          'ordinaryRecordNum',
          'keyRecordNum',
          'freePreview',
          'currency',
          'coverVertical',
          'coverHorizontal',
          'authorId',
        ])

        // 保存到本地数据库
        this.logger.log('事务中：保存短剧信息到本地数据库')
        const [{ id }] = await tx
          .insert(dramas)
          .values({
            ...fields,
            albumId: albumId.toString(), // 保存抖音返回的短剧ID
            tagList: createRequest.tagList.join(','), // 标签转为逗号分隔字符串
            introImages: createRequest.introImages?.join(','), // 介绍图片原始URL，逗号分隔
            introImagesOpenPicId: introImagesOpenPicIds.join(','), // 介绍图片开放ID，逗号分隔
            qualification: 1, // 该字段可先忽略，统一传入1即可。备案信息关注record_audit_info传入即可
            originalPrice: createRequest.originalPrice?.toString() || '0.00', // 转换为字符串
            discountPrice: createRequest.discountPrice?.toString() || '0.00', // 转换为字符串
            coverVerticalOpenPicId, // 保存竖版封面开放ID
            coverHorizontalOpenPicId, // 保存横版封面开放ID
          })
          .$returningId()

        this.logger.log(`事务中：数据库保存成功，本地ID: ${id}`)

        return { albumId, localId: id }
      })

      douyinResult = result.albumId
      localId = result.localId

      this.logger.log(`事务完成：短剧创建成功，本地ID: ${localId}, 抖音ID: ${douyinResult}`)
      return localId
    } catch (error) {
      this.logger.error('短剧创建事务失败', error)

      // 如果是抖音API调用失败，直接抛出异常
      if (!douyinResult) {
        this.logger.error('抖音API调用失败，事务回滚')
        throw error
      }

      // 如果抖音API成功但数据库保存失败，需要记录错误并考虑补偿机制
      this.logger.error(`严重错误：抖音短剧已创建(ID: ${douyinResult})，但本地数据库保存失败`, error)
      throw new BizException(ErrorCodeEnum.DRAMA_CREATE_FAILED)
    }
  }

  // 详情
  async getDetail(id: string) {
    const user = getUserContext()
    const where: SQLWrapper[] = [eq(dramas.isDeleted, LogicDelete.NotDeleted), eq(dramas.id, id)]

    if (user.role !== Role.ADMIN) {
      where.push(eq(dramas.authorId, user.id))
    }

    const drama = await this.drizzle.db.query.dramas.findFirst({ where: and(...where) })
    return toResponse(DramaResponse, drama)
  }

  // 更新
  async update(
    id: string,
    updateRequest: DramaUpdateRequest,
  ): Promise<{ id: string; albumId: string; version: number }> {
    this.logger.log(`开始更新短剧: ${updateRequest.title}`)

    // 检查短剧是否存在
    const currentUserId = getUserId()
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })
    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 检查是否越权
    const user = getUserContext()
    if (user.role !== Role.ADMIN && drama.authorId !== currentUserId) {
      throw new BizException(ErrorCodeEnum.NO_PERMISSION)
    }

    // 检查短剧是否有抖音ID
    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 判断短剧是否已经提审，已经提审了的剧无法修改，必须等审出后才能修改
    // 版本增加的时机是审核完成后再次编辑，在下一次提审之前的多次修改都不会改变短剧的版本

    // 第一步：上传封面图片到抖音
    this.logger.log('开始上传封面图片到抖音')
    const coverUploadTasks = [
      this.douyinDramaManagementProvider.uploadImage(updateRequest.coverVertical),
      this.douyinDramaManagementProvider.uploadImage(updateRequest.coverHorizontal),
    ]

    // 上传成本分配图片
    const costDistributionOpenPicId = await this.douyinDramaManagementProvider.uploadImage(
      updateRequest.costDistributionUri,
    )

    // 上传承诺书图片（如果有）
    let assuranceOpenPicId: string | undefined
    if (updateRequest.assuranceUri) {
      assuranceOpenPicId = await this.douyinDramaManagementProvider.uploadImage(updateRequest.assuranceUri)
    }

    // 上传介绍图片到抖音（如果有）
    let introImagesOpenPicIds: string[] = []
    if (updateRequest.introImages && updateRequest.introImages.length > 0) {
      this.logger.log(`开始上传 ${updateRequest.introImages.length} 张介绍图片到抖音`)
      const introUploadTasks = updateRequest.introImages.map((imageUrl) =>
        this.douyinDramaManagementProvider.uploadImage(imageUrl),
      )
      introImagesOpenPicIds = await Promise.all(introUploadTasks)
      this.logger.log(`介绍图片上传完成，获得 ${introImagesOpenPicIds.length} 个开放图片ID`)
    }

    const [coverVerticalOpenPicId, coverHorizontalOpenPicId] = await Promise.all(coverUploadTasks)
    this.logger.log(`封面图片上传完成，竖版: ${coverVerticalOpenPicId}, 横版: ${coverHorizontalOpenPicId}`)

    // 第二步：构建抖音短剧编辑请求
    const albumInfo = {
      title: updateRequest.title,
      seq_num: updateRequest.seqNum,
      cover_list: [coverVerticalOpenPicId, coverHorizontalOpenPicId],
      year: updateRequest.year,
      album_status: updateRequest.albumStatus, // 短剧更新状态，用户填
      recommendation: updateRequest.recommendation,
      desp: updateRequest.desp,
      tag_list: updateRequest.tagList.map(String), // 转换为字符串数组
      qualification: 1, // 该字段可先忽略，统一传入1即可。备案信息关注record_audit_info传入即可
      record_audit_info: {
        record_material: {
          name: updateRequest.title,
          duration: updateRequest.duration,
          seqs_count: updateRequest.seqNum,
          production_organisation: updateRequest.productionOrganisation,
          director: [updateRequest.director],
          producer: [updateRequest.producer],
          actor: [updateRequest.actor],
          summary: updateRequest.summary,
          cost_distribution_uri: costDistributionOpenPicId,
          playlet_production_cost: updateRequest.playletProductionCost, // 制作成本
          screen_writer: updateRequest.screenWriter ? [updateRequest.screenWriter] : undefined,
          ...(assuranceOpenPicId && { assurance_uri: assuranceOpenPicId }), // 只有存在时才添加
        },
        broadcast_record_info:
          updateRequest.recordType && updateRequest.broadcastRecordNumber
            ? {
                record_type: updateRequest.recordType,
                broadcast_record_number: updateRequest.broadcastRecordNumber,
              }
            : undefined,
      },
    }

    // 第三步：调用抖音编辑短剧接口
    this.logger.log('开始调用抖音编辑短剧接口')
    const douyinResult = await this.douyinDramaManagementProvider.editDrama(
      drama.albumId,
      albumInfo,
      undefined, // 暂时不处理剧集信息
    )
    const version = douyinResult.version
    this.logger.log(`抖音短剧编辑成功，短剧ID: ${douyinResult.album_id}, 版本: ${version}`)

    // 第四步：更新本地数据库
    this.logger.log('开始更新短剧信息到本地数据库')
    const fields = pick(updateRequest, [
      'title',
      'seqNum',
      'year',
      'albumStatus',
      'recommendation',
      'desp',
      'summary',
      'categoryId',
      'actor',
      'director',
      'duration',
      'productionOrganisation',
      'producer',
      'costDistributionUri',
      'assuranceUri',
      'playletProductionCost',
      'screenWriter',
      'recordType',
      'broadcastRecordNumber',
      'licenseNum',
      'registrationNum',
      'ordinaryRecordNum',
      'keyRecordNum',
      'freePreview',
      'currency',
      'coverVertical',
      'coverHorizontal',
      'authorId',
    ])

    await this.drizzle.db
      .update(dramas)
      .set({
        ...fields,
        version: version.valueOf(),
        tagList: updateRequest.tagList.join(','), // 标签转为逗号分隔字符串
        introImages: updateRequest.introImages?.join(','), // 介绍图片原始URL，逗号分隔
        introImagesOpenPicId: introImagesOpenPicIds.join(','), // 介绍图片开放ID，逗号分隔
        qualification: 1, // 该字段可先忽略，统一传入1即可。备案信息关注record_audit_info传入即可
        originalPrice: updateRequest.originalPrice?.toString() || '0.00', // 转换为字符串
        discountPrice: updateRequest.discountPrice?.toString() || '0.00', // 转换为字符串
        coverVerticalOpenPicId, // 保存竖版封面开放ID
        coverHorizontalOpenPicId, // 保存横版封面开放ID
      })
      .where(eq(dramas.id, id))

    this.logger.log(`短剧更新完成，本地ID: ${id}, 抖音ID: ${drama.albumId}`)

    // TODO 看是否需要这块逻辑 编辑成功后重置审核状态为 pending
    await this.drizzle.db
      .update(dramas)
      .set({
        douyinAuditStatus: 'pending',
        platformAuditStatus: 'pending',
      })
      .where(eq(dramas.id, id))

    return {
      id,
      albumId: drama.albumId,
      version: douyinResult.version, // 抖音返回的版本号
    }
  }

  // 送审
  async submitForReview(id: string): Promise<{ id: string; albumId: string; version: number }> {
    this.logger.log(`开始送审短剧: ${id}`)

    // 检查短剧是否存在
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })
    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 检查短剧是否有抖音ID
    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 检查是否正在审核中（防重复送审）
    if (drama.douyinAuditStatus === 'reviewing') {
      throw new BizException(ErrorCodeEnum.DRAMA_REVIEW_IN_PROGRESS)
    }

    // 检查短剧下是否有分集
    const episodeCountResult = await this.drizzle.db
      .select({ count: count() })
      .from(episodes)
      .where(and(eq(episodes.dramaId, id), eq(episodes.isDeleted, LogicDelete.NotDeleted)))

    const episodeCount = episodeCountResult[0]?.count || 0
    if (episodeCount === 0) {
      throw new BizException(ErrorCodeEnum.DRAMA_NO_EPISODES)
    }

    this.logger.log(`短剧包含 ${episodeCount} 个分集，可以送审`)

    // 调用抖音送审接口
    this.logger.log('开始调用抖音送审接口')
    const douyinResult = await this.douyinDramaManagementProvider.submitDramaForReview(drama.albumId)
    if (douyinResult?.version == null) {
      // 判断返回结果是否有version字段，没有则送审失败
      throw new BizException(ErrorCodeEnum.DRAMA_REVIEW_FAILED)
    }
    this.logger.log(`抖音送审成功，获得版本号: ${douyinResult.version}`)

    // 更新数据库状态
    await this.drizzle.db
      .update(dramas)
      .set({
        version: douyinResult.version,
        douyinAuditStatus: 'reviewing',
        platformAuditStatus: 'approved', // 平台审核已通过
      })
      .where(eq(dramas.id, id))

    this.logger.log(`短剧送审完成，本地ID: ${id}, 抖音ID: ${drama.albumId}, 版本: ${douyinResult.version}`)

    return {
      id,
      albumId: drama.albumId,
      version: douyinResult.version,
    }
  }

  // ====================== 上线管理 ======================

  /**
   * 短剧上线
   * @param id 短剧本地ID
   * @param version 上线版本号（可选，默认使用当前版本）
   * @returns 上线结果
   * @throws {BizException} 当上线失败时抛出异常
   */
  async onlineDrama(id: string, version?: number) {
    this.logger.log(`收到短剧上线请求: ${id}`)

    // 查询短剧信息
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    // 验证短剧是否存在
    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 验证短剧是否有抖音短剧ID
    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN)
    }

    // 如果没有指定版本，使用当前版本
    const targetVersion = version || drama.version
    if (!targetVersion) {
      throw new BizException(ErrorCodeEnum.DRAMA_NO_VERSION)
    }

    this.logger.log(`开始上线短剧: albumId=${drama.albumId}, version=${targetVersion}`)

    // 调用抖音上线接口
    const douyinResult = await this.douyinDramaManagementProvider.onlineDrama(drama.albumId, targetVersion)

    // 更新本地数据库状态
    await this.drizzle.db
      .update(dramas)
      .set({
        onlineVersion: douyinResult.version,
        onlineStatus: 0, // 0表示上线
        onlineTime: new Date(),
        douyinPublishStatus: 'online',
      })
      .where(eq(dramas.id, id))

    this.logger.log(`短剧上线成功: 本地ID=${id}, 抖音ID=${drama.albumId}, 版本=${douyinResult.version}`)

    return {
      id,
      albumId: drama.albumId,
      version: douyinResult.version,
      status: 'online',
    }
  }

  /**
   * 短剧下线
   * @param id 短剧本地ID
   * @param version 下线版本号（可选，默认使用当前线上版本）
   * @returns 下线结果
   * @throws {BizException} 当下线失败时抛出异常
   */
  async offlineDrama(id: string, version?: number) {
    this.logger.log(`收到短剧下线请求: ${id}`)

    // 查询短剧信息
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN)
    }

    // 如果没有指定版本，使用当前线上版本
    const targetVersion = version || drama.onlineVersion
    if (!targetVersion) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_ONLINE)
    }

    this.logger.log(`开始下线短剧: albumId=${drama.albumId}, version=${targetVersion}`)

    // 调用抖音下线接口
    const douyinResult = await this.douyinDramaManagementProvider.offlineDrama(drama.albumId, targetVersion)

    // 更新本地数据库状态
    await this.drizzle.db
      .update(dramas)
      .set({
        onlineStatus: 1, // 1表示下线
        douyinPublishStatus: 'offline',
      })
      .where(eq(dramas.id, id))

    this.logger.log(`短剧下线成功: 本地ID=${id}, 抖音ID=${drama.albumId}, 版本=${douyinResult.version}`)

    return {
      id,
      albumId: drama.albumId,
      version: douyinResult.version,
      status: 'offline',
    }
  }

  /**
   * 查询短剧线上状态
   * @param id 短剧本地ID
   * @returns 线上状态信息
   * @throws {BizException} 当查询失败时抛出异常
   */
  async queryDramaOnlineStatus(id: string) {
    this.logger.log(`查询短剧线上状态: ${id}`)

    // 查询短剧信息
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN)
    }

    // 调用抖音查询接口
    const douyinResult = await this.douyinDramaManagementProvider.queryDramaOnlineStatus(drama.albumId)

    // 同步更新本地数据库状态
    await this.drizzle.db
      .update(dramas)
      .set({
        onlineVersion: douyinResult.version,
        onlineStatus: douyinResult.playlet_status,
        douyinPublishStatus: douyinResult.playlet_status === PlayletStatus.ONLINE ? 'online' : 'offline',
      })
      .where(eq(dramas.id, id))

    this.logger.log(
      `查询短剧线上状态成功: 本地ID=${id}, 抖音ID=${drama.albumId}, 版本=${douyinResult.version}, 状态=${douyinResult.playlet_status}`,
    )

    return {
      id,
      albumId: drama.albumId,
      version: douyinResult.version,
      playletStatus: douyinResult.playlet_status,
      status: douyinResult.playlet_status === PlayletStatus.ONLINE ? 'online' : 'offline',
    }
  }

  // 删除
  async delete(id: string) {
    const currentUserId = getUserId()
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    if (drama.authorId !== currentUserId) {
      throw new BizException(ErrorCodeEnum.NO_PERMISSION)
    }

    const [result] = await this.drizzle.db
      .update(dramas)
      .set({ isDeleted: LogicDelete.Deleted })
      .where(eq(dramas.id, id))

    this.logger.log(`短剧删除: ${id}，操作人: ${currentUserId}`)

    return result?.affectedRows > 0
  }

  // ====================== 查询和同步管理（补偿机制) ======================

  /**
   * 同步所有短剧数据（优化版本：批量查询 + 并行更新）
   * @returns 同步结果统计
   * @throws {BizException} 当同步失败时抛出异常
   */
  async syncAllDramas() {
    const startTime = new Date()
    this.logger.log('开始同步所有短剧数据')

    try {
      // 1. 获取抖音平台所有短剧
      this.logger.log('正在获取抖音平台所有短剧数据...')
      const douyinData = await this.douyinDramaManagementProvider.fetchAllDramasComplete()
      this.logger.log(`获取到${douyinData.total}个抖音短剧`)

      if (douyinData.album_info_list.length === 0) {
        this.logger.log('没有找到任何短剧数据')
        return {
          total: 0,
          updated: 0,
          new: 0,
          failures: 0,
          failureDetails: [],
          duration: Date.now() - startTime.getTime(),
        }
      }

      // 2. 批量查询本地所有相关短剧
      this.logger.log('正在批量查询本地短剧数据...')
      const albumIds = douyinData.album_info_list.map((item) => item.album_audit_info.album_id.toString())
      const localDramas = await this.drizzle.db.query.dramas.findMany({
        where: and(inArray(dramas.albumId, albumIds), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
        columns: {
          id: true,
          albumId: true,
          title: true,
        },
      })

      this.logger.log(`找到${localDramas.length}个本地短剧记录`)

      // 3. 创建映射表，提高查找效率
      const localDramaMap = new Map(localDramas.map((drama) => [drama.albumId, drama]))

      // 4. 准备更新任务数据
      const updateTasks: Array<{
        albumInfo: FetchAlbumInfo
        localDrama: { id: string; albumId: string | null; title: string }
      }> = []
      let newCount = 0

      // 遍历抖音短剧数据，分类处理
      for (const albumInfo of douyinData.album_info_list) {
        const albumId = albumInfo.album_audit_info.album_id.toString()
        const localDrama = localDramaMap.get(albumId)

        if (localDrama) {
          // 找到本地对应记录，收集到更新任务列表
          updateTasks.push({ albumInfo, localDrama })
        } else {
          // 本地没有对应记录，记录为新发现的短剧
          newCount++
          this.logger.log(`发现新短剧: ${albumInfo.title} (${albumId}) - 需要手动处理`)
        }
      }

      this.logger.log(`准备更新${updateTasks.length}个短剧，发现${newCount}个新短剧`)

      // 5. 分批并行处理更新操作（避免数据库连接池耗尽和内存压力）
      const BATCH_SIZE = 50 // 每批处理50个短剧，平衡性能和资源使用
      const batches: Array<Array<(typeof updateTasks)[0]>> = []

      // 将更新任务分割成多个批次
      for (let i = 0; i < updateTasks.length; i += BATCH_SIZE) {
        batches.push(updateTasks.slice(i, i + BATCH_SIZE))
      }

      this.logger.log(`分${batches.length}批处理，每批最多${BATCH_SIZE}个短剧`)

      // 6. 逐批处理，批次内并行执行
      let totalUpdatedCount = 0
      const allFailures: Array<{ albumId: string; title: string; error: string }> = []

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex]
        const batchNumber = batchIndex + 1

        this.logger.log(`开始处理第${batchNumber}/${batches.length}批，包含${batch.length}个短剧`)

        // 创建当前批次的更新Promise数组
        const batchPromises = batch.map((task) => this.updateDramaFromDouyinSafe(task.localDrama.id, task.albumInfo))

        // 并行执行当前批次的所有更新操作
        const batchResults = await Promise.allSettled(batchPromises)

        // 处理当前批次的结果
        let batchUpdatedCount = 0
        const batchFailures: Array<{ albumId: string; title: string; error: string }> = []

        batchResults.forEach((result, index) => {
          const task = batch[index]
          if (!task) return

          const { albumInfo, localDrama } = task
          const albumId = albumInfo.album_audit_info.album_id.toString()

          if (result.status === 'fulfilled') {
            // 更新成功
            batchUpdatedCount++
            this.logger.log(`更新短剧成功: ${albumInfo.title} (${albumId})`)
          } else {
            // 更新失败，记录详细错误信息
            const errorMessage = result.reason instanceof Error ? result.reason.message : 'Unknown error'
            const errorStack = result.reason instanceof Error ? result.reason.stack : undefined

            const failure = {
              albumId,
              title: albumInfo.title,
              error: errorMessage,
            }
            batchFailures.push(failure)

            // 记录详细的错误日志，包含上下文信息
            this.logger.error(`同步短剧失败: ${failure.title} (${failure.albumId})`, {
              albumId: failure.albumId,
              title: failure.title,
              localDramaId: localDrama.id,
              error: failure.error,
              stack: errorStack,
              batchNumber,
              timestamp: new Date().toISOString(),
              operation: 'syncAllDramas',
            })
          }
        })

        // 累计统计信息
        totalUpdatedCount += batchUpdatedCount
        allFailures.push(...batchFailures)

        // 记录当前批次处理结果
        const batchSuccessRate = ((batchUpdatedCount / batch.length) * 100).toFixed(1)
        this.logger.log(
          `第${batchNumber}批处理完成: 成功${batchUpdatedCount}个，失败${batchFailures.length}个，成功率${batchSuccessRate}%`,
        )

        // 批次间短暂延迟，减轻数据库压力（最后一批不需要延迟）
        if (batchIndex < batches.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 100)) // 延迟100ms
          this.logger.log(`批次间延迟100ms，准备处理下一批...`)
        }
      }

      // 7. 计算最终统计结果
      const duration = Date.now() - startTime.getTime()
      const totalProcessed = totalUpdatedCount + allFailures.length
      const successRate = totalProcessed > 0 ? ((totalUpdatedCount / totalProcessed) * 100).toFixed(2) : '0.00'

      // 记录最终同步结果
      this.logger.log(
        `同步完成: 总计${douyinData.total}个短剧，更新${totalUpdatedCount}个，发现${newCount}个新短剧，失败${allFailures.length}个，成功率${successRate}%，耗时${duration}ms`,
      )

      // 如果有失败的记录，额外记录失败摘要
      if (allFailures.length > 0) {
        this.logger.warn(`同步失败摘要: 共${allFailures.length}个短剧同步失败`, {
          failedAlbumIds: allFailures.map((f) => f.albumId),
          operation: 'syncAllDramas',
          timestamp: new Date().toISOString(),
        })
      }

      return {
        total: douyinData.total,
        updated: totalUpdatedCount,
        new: newCount,
        failures: allFailures.length,
        failureDetails: allFailures,
        duration,
        successRate: parseFloat(successRate),
      }
    } catch (error) {
      const duration = Date.now() - startTime.getTime()
      this.logger.error('同步所有短剧数据失败', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        duration,
        operation: 'syncAllDramas',
      })
      throw error
    }
  }

  /**
   * 同步单个短剧的审核状态
   * @param id 短剧本地ID
   * @returns 同步结果
   * @throws {BizException} 当同步失败时抛出异常
   */
  async syncDramaAuditStatus(id: string) {
    this.logger.log(`同步短剧审核状态: ${id}`)

    // 查询短剧信息
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN)
    }

    // 查询抖音平台的最新状态
    const douyinData = await this.douyinDramaManagementProvider.fetchDramaVersions(drama.albumId, 0, 1)

    if (douyinData.album_info_list.length === 0) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 同步最新version，抖音官方返回的肯定是最新的，所以这里同步一下
    const latestVersion = douyinData.album_info_list[0]
    await this.updateDramaFromDouyin(id, latestVersion)

    this.logger.log(`同步短剧审核状态成功: ${id}`)

    return {
      id,
      albumId: drama.albumId,
      version: latestVersion.album_audit_info.version,
      auditStatus: latestVersion.album_audit_info.audit_status,
      status: latestVersion.album_audit_info.status,
    }
  }

  /**
   * 查询短剧版本信息
   * @param id 短剧本地ID
   * @param offset 分页偏移量
   * @param limit 分页限制数量
   * @returns 版本信息列表
   * @throws {BizException} 当查询失败时抛出异常
   */
  async getDramaVersions(id: string, offset: number = 0, limit: number = 20) {
    this.logger.log(`查询短剧版本信息: ${id}`)

    // 查询短剧信息
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN)
    }

    // 查询抖音平台的版本信息
    const douyinData = await this.douyinDramaManagementProvider.fetchDramaVersions(drama.albumId, offset, limit)

    this.logger.log(`查询短剧版本信息成功: ${id}, 返回${douyinData.album_info_list.length}个版本`)

    return {
      id,
      albumId: drama.albumId,
      total: douyinData.total,
      versions: douyinData.album_info_list.map((version) => ({
        version: version.album_audit_info.version,
        status: version.album_audit_info.status,
        auditStatus: version.album_audit_info.audit_status,
        auditMsg: version.album_audit_info.audit_msg,
        title: version.title,
        seqNum: version.seq_num,
        year: version.year,
        albumStatus: version.album_status,
      })),
    }
  }

  /**
   * 查询短剧剧集信息
   * @param id 短剧本地ID
   * @param version 版本号（可选，默认使用当前版本）
   * @param offset 分页偏移量
   * @param limit 分页限制数量
   * @returns 剧集信息列表
   * @throws {BizException} 当查询失败时抛出异常
   */
  async getDramaEpisodes(id: string, version?: number, offset: number = 0, limit: number = 20) {
    this.logger.log(`查询短剧剧集信息: ${id}`)

    // 查询短剧信息
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, id), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN)
    }

    // 如果没有指定版本，使用当前版本
    const targetVersion = version || drama.version
    if (targetVersion == null) {
      throw new BizException(ErrorCodeEnum.DRAMA_NO_VERSION)
    }

    // 查询抖音平台的剧集信息
    const douyinData = await this.douyinDramaManagementProvider.fetchDramaEpisodes(
      drama.albumId,
      targetVersion,
      offset,
      limit,
    )

    this.logger.log(
      `查询短剧剧集信息成功: ${id}, 版本${targetVersion}, 返回${douyinData.episode_info_list.length}个剧集`,
    )

    return {
      id,
      albumId: drama.albumId,
      version: targetVersion,
      total: douyinData.total,
      // 注意返回的分集属性都是抖音官方返回的，最新数据
      episodes: douyinData.episode_info_list.map((episode) => ({
        title: episode.title,
        seq: episode.seq,
        coverList: episode.cover_list,
        openVideoId: episode.open_video_id,
        episodeId: episode.episode_audit_info.episode_id,
        version: episode.episode_audit_info.version,
        status: episode.episode_audit_info.status,
        auditStatus: episode.episode_audit_info.audit_status,
        // 能力列表
        scopeList: episode.episode_audit_info.scope_list,
        auditMsg: episode.episode_audit_info.audit_msg,
      })),
    }
  }

  /**
   * 安全地从抖音数据更新本地短剧信息（用于并行处理）
   * @param localId 本地短剧ID
   * @param albumInfo 抖音短剧信息
   * @private
   */
  private async updateDramaFromDouyinSafe(localId: string, albumInfo: FetchAlbumInfo): Promise<void> {
    try {
      await this.updateDramaFromDouyin(localId, albumInfo)
    } catch (error) {
      // 重新抛出错误，让Promise.allSettled捕获
      throw new Error(`更新短剧失败 (ID: ${localId}): ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 从抖音数据更新本地短剧信息
   * @param localId 本地短剧ID
   * @param albumInfo 抖音短剧信息
   * @private
   */
  private async updateDramaFromDouyin(localId: string, albumInfo: FetchAlbumInfo) {
    const auditInfo = albumInfo.album_audit_info

    // 映射审核状态
    let douyinAuditStatus = 'pending'
    if (auditInfo.audit_status === DramaAuditStatus.AUDIT_PASSED) {
      douyinAuditStatus = 'approved'
    } else if (auditInfo.audit_status === DramaAuditStatus.AUDIT_FAILED) {
      douyinAuditStatus = 'rejected'
    } else if (auditInfo.audit_status === DramaAuditStatus.AUDITING) {
      douyinAuditStatus = 'reviewing'
    }

    await this.drizzle.db
      .update(dramas)
      .set({
        // 同步版本
        version: auditInfo.version,
        // 抖音审核状态，审核消息，最新一次审核时间
        douyinAuditStatus,
        auditMsg: auditInfo.audit_msg || null,
        lastAuditTime: new Date(),
        // TODO 从抖音接口传过来的是最新数据，那这里理应同步基本信息 待确认
        title: albumInfo.title,
        seqNum: albumInfo.seq_num,
        year: albumInfo.year,
        recommendation: albumInfo.recommendation,
        // 同步 短剧更新状态 和 资质状态
        albumStatus: albumInfo.album_status,
        qualification: albumInfo.qualification,
      })
      .where(eq(dramas.id, localId))
  }
}
