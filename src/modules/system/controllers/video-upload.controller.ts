import { Auth } from '@/common/decorators/auth.decorator'
import { AdminOrCreator } from '@/common/decorators/roles.decorator'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Get, Logger, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags, ApiQuery } from '@nestjs/swagger'
import { VideoUploadRequest, ImageUploadRequest } from '../requests/video-upload.request'
import { VideoUploadService } from '../services/video-upload.service'
import { VideoUploadStatus } from '@/common/drizzle/schema/video-uploads'

@ApiTags('资源上传')
@Auth()
@Controller('/admin/uploads')
export class VideoUploadController {
  private readonly logger = new Logger(VideoUploadController.name)

  constructor(private readonly videoUploadService: VideoUploadService) {}

  @Post('/video')
  @AdminOrCreator()
  @ApiOperation({ summary: '上传视频到抖音平台' })
  async uploadVideo(@Body() request: VideoUploadRequest) {
    this.logger.log(`收到视频上传请求: ${request.title}`)

    const result = await this.videoUploadService.uploadVideo(request)

    this.logger.log(`视频上传请求提交成功: uploadId=${result.id}`)
    return CommonResponse.ok(result, '视频上传请求已提交，请等待处理完成')
  }

  @Post('/image')
  @AdminOrCreator()
  @ApiOperation({ summary: '上传图片到抖音平台' })
  async uploadImage(@Body() request: ImageUploadRequest) {
    this.logger.log(`收到图片上传请求: ${request.imageUrl}`)

    const result = await this.videoUploadService.uploadImage(request)

    this.logger.log(`图片上传成功: openPicId=${result.openPicId}`)
    return CommonResponse.ok(result, '图片上传成功')
  }

  @Get('/videos')
  @AdminOrCreator()
  @ApiOperation({ summary: '查询用户的视频上传记录' })
  @ApiQuery({ name: 'status', required: false, enum: VideoUploadStatus, description: '上传状态筛选' })
  async getUserVideoUploads(@Query('status') status?: string) {
    this.logger.log(`查询用户视频上传记录, status=${status}`)

    const statusNum = status !== undefined ? parseInt(status, 10) : undefined
    const result = await this.videoUploadService.getUserVideoUploads(statusNum)

    return CommonResponse.ok(result)
  }
}
