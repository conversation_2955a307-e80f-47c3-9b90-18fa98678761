import { Auth } from '@/common/decorators/auth.decorator'
import { Admin, AdminOrCreator } from '@/common/decorators/roles.decorator'
import { CommonResponse } from '@/common/responses/common.response'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { getUserContext } from '@/context/user.context'
import { Role } from '@/modules/auth/interfaces/jwt-payload.interface'
import { Body, Controller, Delete, Get, Param, Post, Put, Query, Logger } from '@nestjs/common'
import { ApiTags, ApiOperation } from '@nestjs/swagger'
import { DramaCreateRequest, DramaFilterRequest, DramaUpdateRequest } from '../requests/drama.request'
import { DramaService } from '../services/drama.service'

@ApiTags('短剧管理')
@Auth()
@Controller('/admin/dramas')
export class DramaController {
  private readonly logger = new Logger(DramaController.name)

  constructor(private readonly dramaService: DramaService) {}

  @Get()
  @AdminOrCreator()
  async adminDramasList(@Query() filterRequest: DramaFilterRequest) {
    const list = await this.dramaService.list(filterRequest)
    return CommonResponse.ok(list)
  }

  @Post()
  @AdminOrCreator()
  @ApiOperation({ summary: '创建短剧' })
  async create(@Body() createRequest: DramaCreateRequest) {
    // 校验URL是否重复
    this.validateUrlsUnique(createRequest)

    const id = await this.dramaService.create(createRequest)
    return CommonResponse.ok({ id })
  }

  @Get('/:id')
  @AdminOrCreator()
  async getDetail(@Param('id') id: string) {
    const result = await this.dramaService.getDetail(id)
    if (!result) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }
    return CommonResponse.ok(result)
  }

  @Put('/:id')
  @AdminOrCreator()
  @ApiOperation({ summary: '编辑短剧' })
  async update(@Param('id') id: string, @Body() updateRequest: DramaUpdateRequest) {
    this.logger.log(`收到修改短剧请求: ${updateRequest.title}`)

    // 校验URL是否重复
    this.validateUrlsUnique(updateRequest)

    // 设置创作者ID
    const user = getUserContext()
    if (user.role !== Role.ADMIN) {
      updateRequest.authorId = user.id
      this.logger.log(`设置创作者ID: ${user.id}`)
    }

    // 调用服务层更新短剧
    const result = await this.dramaService.update(id, updateRequest)

    this.logger.log(`短剧修改成功: ${updateRequest.title}`)
    return CommonResponse.ok(result)
  }

  @Post('/:id/submit-review')
  @Admin()
  @ApiOperation({ summary: '短剧送审' })
  async submitForReview(@Param('id') id: string) {
    this.logger.log(`收到短剧送审请求: ${id}`)

    // 调用服务层送审
    const result = await this.dramaService.submitForReview(id)

    this.logger.log(`短剧送审成功: ${id}`)
    return CommonResponse.ok(result)
  }

  @Post('/:id/online')
  @Admin()
  @ApiOperation({ summary: '短剧上线' })
  async onlineDrama(@Param('id') id: string, @Body() body?: { version?: number }) {
    this.logger.log(`收到短剧上线请求: ${id}`)

    // 调用服务层上线
    const result = await this.dramaService.onlineDrama(id, body?.version)

    this.logger.log(`短剧上线成功: ${id}`)
    return CommonResponse.ok(result)
  }

  @Post('/:id/offline')
  @Admin()
  @ApiOperation({ summary: '短剧下线' })
  async offlineDrama(@Param('id') id: string, @Body() body?: { version?: number }) {
    this.logger.log(`收到短剧下线请求: ${id}`)

    // 调用服务层下线
    const result = await this.dramaService.offlineDrama(id, body?.version)

    this.logger.log(`短剧下线成功: ${id}`)
    return CommonResponse.ok(result)
  }

  @Get('/:id/online-status')
  @Admin()
  @ApiOperation({ summary: '查询短剧线上状态' })
  async queryOnlineStatus(@Param('id') id: string) {
    this.logger.log(`查询短剧线上状态: ${id}`)

    // 调用服务层查询
    const result = await this.dramaService.queryDramaOnlineStatus(id)

    this.logger.log(`查询短剧线上状态成功: ${id}`)
    return CommonResponse.ok(result)
  }

  @Post('/sync')
  @Admin()
  @ApiOperation({ summary: '同步所有短剧数据' })
  async syncAllDramas() {
    this.logger.log('收到同步所有短剧数据请求')

    // 调用服务层同步
    const result = await this.dramaService.syncAllDramas()

    this.logger.log(
      `同步所有短剧数据成功: 总计${result.total}个短剧，更新${result.updated}个，发现${result.new}个新短剧，失败${result.failures}个`,
    )
    return CommonResponse.ok(result)
  }

  @Post('/:id/sync-audit-status')
  @Admin()
  @ApiOperation({ summary: '同步短剧审核状态' })
  async syncDramaAuditStatus(@Param('id') id: string) {
    this.logger.log(`收到同步短剧审核状态请求: ${id}`)

    // 调用服务层同步
    const result = await this.dramaService.syncDramaAuditStatus(id)

    this.logger.log(`同步短剧审核状态成功: ${id}`)
    return CommonResponse.ok(result)
  }

  @Get('/:id/versions')
  @Admin()
  @ApiOperation({ summary: '查询短剧版本信息' })
  async getDramaVersions(@Param('id') id: string, @Query('offset') offset?: string, @Query('limit') limit?: string) {
    this.logger.log(`查询短剧版本信息: ${id}`)

    const offsetNum = offset ? parseInt(offset, 10) : 0
    const limitNum = limit ? parseInt(limit, 10) : 20

    // 调用服务层查询
    const result = await this.dramaService.getDramaVersions(id, offsetNum, limitNum)

    this.logger.log(`查询短剧版本信息成功: ${id}`)
    return CommonResponse.ok(result)
  }

  @Get('/:id/episodes')
  @AdminOrCreator()
  @ApiOperation({ summary: '查询短剧剧集信息' })
  async getDramaEpisodes(
    @Param('id') id: string,
    @Query('version') version?: string,
    @Query('offset') offset?: string,
    @Query('limit') limit?: string,
  ) {
    this.logger.log(`查询短剧剧集信息: ${id}`)

    const versionNum = version ? parseInt(version, 10) : undefined
    const offsetNum = offset ? parseInt(offset, 10) : 0
    const limitNum = limit ? parseInt(limit, 10) : 20

    // 调用服务层查询
    const result = await this.dramaService.getDramaEpisodes(id, versionNum, offsetNum, limitNum)

    this.logger.log(`查询短剧剧集信息成功: ${id}`)
    return CommonResponse.ok(result)
  }

  @Delete('/:id')
  @AdminOrCreator()
  async delete(@Param('id') id: string) {
    const result = await this.dramaService.delete(id)
    return CommonResponse.ok(result)
  }

  /**
   * 校验URL是否重复
   * 检查coverVertical、coverHorizontal和introImages中的URL是否有重复
   * @param request 创建或更新请求对象
   * @throws {BizException} 当发现重复URL时抛出异常
   */
  private validateUrlsUnique(request: DramaCreateRequest | DramaUpdateRequest): void {
    const urls: string[] = []

    // 添加竖版封面URL
    if (request.coverVertical) {
      urls.push(request.coverVertical)
    }

    // 添加横版封面URL
    if (request.coverHorizontal) {
      urls.push(request.coverHorizontal)
    }

    // 添加介绍图片URLs
    if (request.introImages && Array.isArray(request.introImages)) {
      urls.push(...request.introImages)
    }

    // 检查是否有重复的URL
    const uniqueUrls = new Set(urls)
    if (uniqueUrls.size !== urls.length) {
      this.logger.warn(`发现重复的URL: ${JSON.stringify(urls)}`)
      throw new BizException(ErrorCodeEnum.DRAMA_DUPLICATE_URLS)
    }
  }
}
