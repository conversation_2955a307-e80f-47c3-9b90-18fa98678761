/**
 * 抖音短剧资源管理接口定义
 *
 * API文档: https://bytedance.larkoffice.com/wiki/SG1MwJyWhiqwUxkkZvjcConanue
 *
 * <AUTHOR>
 * @since 2024
 */

// ====================== 常量定义 ======================

/** API端点枚举 */
export const API_ENDPOINTS = {
  /** 资源上传 */
  RESOURCE_UPLOAD: '/playlet/v2/resource/upload/',
  /** 视频查询 */
  VIDEO_QUERY: '/playlet/v2/video/query/',
  /** 创建短剧 */
  CREATE_DRAMA: '/playlet/v2/video/create/',
  /** 编辑短剧 */
  EDIT_DRAMA: '/playlet/v2/video/edit/',
  /** 短剧授权/移除授权 */
  DRAMA_AUTHORIZATION: '/playlet/v2/video/authorize/',
  /** 授权查询功能 */
  AUTHORIZATION_QUERY: '/playlet/v2/auth/authorize/query/',
  /** 页面绑定 */
  PAGE_BIND: '/playlet/v2/album/bind/',
  /** 短剧送审 */
  DRAMA_REVIEW: '/playlet/v2/video/review/',
  /** 短剧上线/下线/查询线上版本 */
  DRAMA_ONLINE: '/playlet/v2/album/online/',
  /** 短剧查询 */
  DRAMA_FETCH: '/playlet/v2/album/fetch/',
} as const

// ====================== 枚举定义 ======================

/** 资源类型枚举 */
export enum ResourceType {
  /** 视频 */
  VIDEO = 1,
  /** 图片 */
  IMAGE = 2,
}

/** 视频ID类型枚举 */
export enum VideoIdType {
  /** 开放视频ID */
  OPEN_VIDEO_ID = 1,
  /** 云端视频ID */
  DY_CLOUD_ID = 2,
}

/** 短剧状态枚举 */
export enum AlbumStatus {
  /** 未上映 */
  NOT_RELEASED = 1,
  /** 更新中 */
  UPDATING = 2,
  /** 已完结 */
  COMPLETED = 3,
}

/** 视频状态枚举 */
export enum VideoStatus {
  /** 处理中 */
  PROCESSING = 'processing',
  /** 成功 */
  SUCCESS = 'success',
  /** 失败 */
  FAILED = 'failed',
}

/** 授权操作类型枚举 */
export enum AuthorizationType {
  /** 全量式授权（替换） */
  FULL = 'full',
  /** 追加式授权 */
  INCREMENT = 'increment',
  /** 移除授权 */
  REMOVE = 'remove',
}

/** 记录类型枚举 */
export enum RecordType {
  /** 普通备案 */
  ORDINARY = 1,
  /** 重点备案 */
  KEY = 2,
}

/** 短剧上线操作类型枚举 */
export enum DramaOnlineOperation {
  /** 查询线上版本 */
  QUERY = 1,
  /** 上线 */
  ONLINE = 2,
  /** 下线 */
  OFFLINE = 3,
}

/** 短剧上下架状态枚举 */
export enum PlayletStatus {
  /** 上线状态 */
  ONLINE = 0,
  /** 下线状态 */
  OFFLINE = 1,
}

/** 短剧查询类型枚举 */
export enum DramaQueryType {
  /** 批量查询所有短剧 */
  BATCH = 1,
  /** 查询单个短剧所有版本 */
  SINGLE = 2,
  /** 查询单个短剧某版本的剧集详情 */
  DETAIL = 3,
}

/** 短剧审核状态枚举 */
export enum DramaAuditStatus {
  /** 未审核 */
  NOT_AUDITED = 99,
  /** 审核中 */
  AUDITING = 98,
  /** 审核未通过 */
  AUDIT_FAILED = 1,
  /** 审核通过 */
  AUDIT_PASSED = 2,
}

/** 短剧提交状态枚举 */
export enum DramaSubmitStatus {
  /** 未提交 */
  NOT_SUBMITTED = 1,
  /** 已提交 */
  SUBMITTED = 2,
  /** 已审出 */
  AUDITED = 3,
}

/** 剧集审核状态枚举 */
export enum EpisodeAuditStatus {
  /** 未审核 */
  NOT_AUDITED = 99,
  /** 审核中 */
  AUDITING = 98,
  /** 不可播放 */
  NOT_PLAYABLE = 1,
  /** 可播放 */
  PLAYABLE = 2,
}

// ====================== 基础类型定义 ======================

/** 抖音API统一响应格式 */
export interface DouyinResponse<T = unknown> {
  /** 响应数据 */
  data: T
  /** 错误消息 */
  err_msg: string
  /** 错误码，0表示成功 */
  err_no: number
  /** 日志ID */
  log_id: string
}

/** 基础请求参数 */
export interface BaseDouyinRequest {
  /** 小程序应用ID */
  ma_app_id: string
}

// ====================== 图片相关 ======================

/** 图片元数据 */
export interface ImageMeta {
  /** 图片URL */
  url: string
  /** 图片格式 (可选) */
  format?: string
}

/** 图片结果 */
export interface ImageResult {
  /** 图片高度 */
  height: number
  /** 开放图片ID */
  open_pic_id: string
  /** 图片宽度 */
  width: number
}

/** 图片上传响应数据 */
export interface ImageUploadData {
  /** 图片结果 */
  image_result: ImageResult
  /** 资源类型 */
  resource_type: ResourceType.IMAGE
}

/** 图片上传请求 */
export interface ImageUploadRequest extends BaseDouyinRequest {
  /** 资源类型 */
  resource_type: ResourceType.IMAGE
  /** 图片元数据 */
  image_meta: ImageMeta
}

// ====================== 视频相关 ======================

/** 视频元数据 */
export interface VideoMeta {
  /** 视频URL */
  url: string
  /** 视频标题 */
  title: string
  /** 视频描述 */
  description?: string
  /** 视频格式 */
  format: string
  /** 是否使用抖音云 */
  use_dy_cloud: boolean
  /** 抖音云ID */
  dy_cloud_id: string
}

/** 视频结果 */
export interface VideoResult {
  /** 开放视频ID */
  open_video_id: string
}

/** 视频上传响应数据 */
export interface VideoUploadData {
  /** 资源类型 */
  resource_type: ResourceType.VIDEO
  /** 视频结果 */
  video_result: VideoResult
}

/** 视频上传请求 */
export interface VideoUploadRequest extends BaseDouyinRequest {
  /** 资源类型 */
  resource_type: ResourceType.VIDEO
  /** 视频元数据 */
  video_meta: VideoMeta
}

/** 视频查询请求 */
export interface VideoQueryRequest extends BaseDouyinRequest {
  /** 视频ID类型 */
  video_id_type: VideoIdType
  /** 开放视频ID (当video_id_type为1时必填) */
  open_video_id?: string
  /** 抖音云ID (当video_id_type为2时必填) */
  dy_cloud_id?: string
}

/** 视频查询响应数据 */
export interface VideoQueryData {
  /** 开放视频ID */
  open_video_id: string
  /** 视频状态 */
  status: VideoStatus
  /** 抖音云ID */
  dy_cloud_id: string
}

// ====================== 短剧相关 ======================

/** 剧集信息 */
export interface EpisodeInfo {
  /** 剧集标题 */
  title: string
  /** 剧集序号 */
  seq: number
  /** 封面图片ID列表 */
  cover_list: string[]
  /** 开放视频ID */
  open_video_id: string
}

/** 备案记录信息 */
export interface RecordInfo {
  /** 许可证号 */
  license_num: string
  /** 登记号 */
  registration_num: string
  /** 普通备案号 */
  ordinary_record_num: string
  /** 重点备案号 */
  key_record_num: string
}

/** 备案审核材料信息 */
export interface RecordMaterial {
  /** 剧名 */
  name: string
  /** 平均单集时长，单位分钟 */
  duration: number
  /** 集数 */
  seqs_count: number
  /** 制作机构 */
  production_organisation: string
  /** 导演列表 */
  director: string[]
  /** 制片人列表 */
  producer: string[]
  /** 演员列表 */
  actor: string[]
  /** 内容简介 */
  summary: string
  /** 成本分配URI */
  cost_distribution_uri: string
  /** 承诺书URI */
  assurance_uri?: string
  /** 微短剧制作成本 */
  playlet_production_cost: number
  /** 编剧列表 */
  screen_writer?: string[]
}

/** 播出备案信息 */
export interface BroadcastRecordInfo {
  /** 备案类型 */
  record_type: RecordType
  /** 播出备案号 */
  broadcast_record_number: string
}

/** 备案审核信息 */
export interface RecordAuditInfo {
  /** 备案材料 */
  record_material: RecordMaterial
  /** 广电备案信息 */
  broadcast_record_info?: BroadcastRecordInfo
}

/** 短剧信息 */
export interface AlbumInfo {
  /** 短剧标题 */
  title: string
  /** 短剧总集数 */
  seq_num: number
  /** 封面图列表 */
  cover_list: string[]
  /** 发行年份 */
  year: number
  /** 短剧状态 */
  album_status: AlbumStatus
  /** 推荐语 */
  recommendation: string
  /** 短剧描述 */
  desp: string
  /** 标签列表 */
  tag_list: string[]
  /** 资质类型 */
  qualification: number
  /** 备案信息 (可选) */
  record_info?: RecordInfo
  /** 备案审核信息 */
  record_audit_info: RecordAuditInfo
}

/** 创建短剧请求 */
export interface CreateDramaRequest extends BaseDouyinRequest {
  /** 短剧信息 */
  album_info: AlbumInfo
}

/** 创建短剧响应数据 */
export interface CreateDramaData {
  /** 短剧ID */
  album_id: string
}

/** 编辑短剧请求 */
export interface EditDramaRequest extends BaseDouyinRequest {
  /** 短剧ID */
  album_id: string
  /** 短剧信息 */
  album_info?: AlbumInfo
  /** 剧集信息列表 */
  episode_info_list?: EpisodeInfo[]
}

/** 编辑短剧响应数据 */
export interface EditDramaData {
  /** 短剧ID */
  album_id: number
  /** 版本号 */
  version: number
  /** 剧集ID映射 */
  episode_id_map: Record<string, number>
}

// ====================== 授权相关 ======================

/** 短剧授权请求参数 */
export interface DramaAuthorizationRequest extends BaseDouyinRequest {
  /** 短剧ID */
  album_id: number | string
  /** 被授权的应用ID列表 */
  app_id_list: string[]
  /** 是否追加式授权，true表示追加，false或不传表示全量替换 */
  increment?: boolean
  /** 是否移除授权，true表示移除指定应用的授权 */
  remove?: boolean
}

/** 短剧授权响应数据 */
export interface DramaAuthorizationData {
  /** 操作结果 */
  success: boolean
}

/** 短剧授权查询请求参数 */
export interface DramaAuthorizationQueryRequest extends BaseDouyinRequest {
  /** 短剧ID */
  album_id: number | string
}

/** 短剧授权查询响应数据 */
export interface DramaAuthorizationQueryData {
  /** 已授权的应用ID列表 */
  app_id_list: string[]
}

// ====================== 页面绑定相关 ======================

/** 绑定类型枚举 */
export enum SchemaBindType {
  /** 单集绑定 */
  SINGLE = 1,
  /** 批量绑定 */
  BATCH = 2,
  /** 查询绑定 */
  QUERY = 3,
}

/** 参数对象 */
export interface Param {
  /** 参数键 */
  key: string
  /** 参数值 */
  value: string
}

/** 单集绑定数据 */
export interface SingleSchemaBind {
  /** 短剧ID */
  album_id: number
  /** 剧集ID */
  episode_id: number
  /** 小程序页面路径 */
  path: string
  /** 参数列表 */
  params?: Param[]
}

/** 查询绑定数据 */
export interface QuerySchemaBind {
  /** 短剧ID */
  album_id: number
}

/** 页面绑定请求参数 */
export interface PageBindRequest {
  /** 绑定类型 */
  schema_bind_type: SchemaBindType
  /** 代操作小程序ID (可选) */
  app_id?: string | null
  /** 单集绑定数据 (当schema_bind_type=1时必填) */
  single_schema_bind?: SingleSchemaBind
  /** 批量绑定数据 (当schema_bind_type=2时必填) */
  schema_bind_list?: SingleSchemaBind[]
  /** 查询绑定数据 (当schema_bind_type=3时必填) */
  query_schema_bind?: QuerySchemaBind
}

/** 页面绑定响应数据 */
export interface PageBindData {
  /** 操作结果 (通常为空对象表示成功) */
  [key: string]: any
}

// ====================== 通用上传类型 ======================

/** 上传响应数据联合类型 */
export type UploadData = ImageUploadData | VideoUploadData

/** 上传请求联合类型 */
export type UploadRequest = ImageUploadRequest | VideoUploadRequest

// ====================== 送审相关 ======================

/** 短剧送审请求 */
export interface DramaReviewRequest extends BaseDouyinRequest {
  /** 短剧ID */
  album_id: number | string
}

/** 短剧送审响应数据 */
export interface DramaReviewData {
  /** 此次送审的版本号 */
  version: number
}

// ====================== 短剧上线相关 ======================

/** 短剧上线请求 */
export interface DramaOnlineRequest extends BaseDouyinRequest {
  /** 短剧ID */
  album_id: number | string
  /** 操作类型 */
  operate: DramaOnlineOperation
  /** 上线的版本（查询线上版本不用传此参数） */
  version?: number
}

/** 短剧上线响应数据 */
export interface DramaOnlineData {
  /** 短剧ID */
  album_id: number
  /** 上线的版本 */
  version: number
}

/** 短剧线上状态查询响应数据 */
export interface DramaOnlineQueryData {
  /** 短剧ID */
  album_id: number
  /** 线上版本号 */
  version: number
  /** 短剧上下架状态 */
  playlet_status: PlayletStatus
}

// ====================== 短剧查询相关 ======================

/** 批量查询参数 */
export interface BatchQueryParams {
  /** 分页拉取的起始偏移量 */
  offset: number
  /** 分页拉取的最大返回结果数 */
  limit: number
}

/** 单个短剧查询参数 */
export interface SingleQueryParams {
  /** 分页拉取的起始偏移量 */
  offset: number
  /** 分页拉取的最大返回结果数 */
  limit: number
  /** 短剧ID */
  album_id: number
}

/** 短剧详情查询参数 */
export interface DetailQueryParams {
  /** 分页拉取的起始偏移量 */
  offset: number
  /** 分页拉取的最大返回结果数 */
  limit: number
  /** 短剧ID */
  album_id: number
  /** 版本号 */
  version: number
}

/** 短剧查询请求 */
export interface DramaFetchRequest extends BaseDouyinRequest {
  /** 查询类型 */
  query_type: DramaQueryType
  /** 批量查询参数（query_type=1时必填） */
  batch_query?: BatchQueryParams
  /** 单个短剧查询参数（query_type=2时必填） */
  single_query?: SingleQueryParams
  /** 短剧详情查询参数（query_type=3时必填） */
  detail_query?: DetailQueryParams
}

/** 短剧审核信息 */
export interface FetchAlbumAuditInfo {
  /** 短剧ID */
  album_id: number
  /** 版本号 */
  version: number
  /** 状态 */
  status: DramaSubmitStatus
  /** 审核状态（query_type为1时此字段不返回） */
  audit_status?: DramaAuditStatus
  /** 审核备注 */
  audit_msg?: string
}

/** 剧集审核信息 */
export interface FetchEpisodeAuditInfo {
  /** 剧集ID */
  episode_id: number
  /** 版本号 */
  version: number
  /** 状态 */
  status: DramaSubmitStatus
  /** 审核状态 */
  audit_status?: EpisodeAuditStatus
  /** 能力列表 */
  scope_list?: string[]
  /** 审核备注 */
  audit_msg?: string
}

/** 查询返回的短剧信息 */
export interface FetchAlbumInfo {
  /** 短剧标题 */
  title: string
  /** 总集数 */
  seq_num: number
  /** 封面图列表 */
  cover_list: string[]
  /** 发行年份 */
  year: number
  /** 短剧更新状态 */
  album_status: AlbumStatus
  /** 短剧推荐语 */
  recommendation: string
  /** 短剧简介 */
  desp: string
  /** 短剧类目标签 */
  tag_list: number[]
  /** 资质状态 */
  qualification: number
  /** 备案信息 */
  record_info?: RecordInfo
  /** 审核信息 */
  album_audit_info: FetchAlbumAuditInfo
}

/** 查询返回的剧集信息 */
export interface FetchEpisodeInfo {
  /** 剧集标题 */
  title: string
  /** 集数 */
  seq: number
  /** 封面图 */
  cover_list: string[]
  /** 剧集对应的视频ID */
  open_video_id: string
  /** 剧集审核信息 */
  episode_audit_info: FetchEpisodeAuditInfo
}

/** 批量查询响应数据 */
export interface BatchData {
  /** 总数 */
  total: number
  /** 短剧信息列表 */
  album_info_list: FetchAlbumInfo[]
}

/** 单个短剧查询响应数据 */
export interface SingleData {
  /** 总数 */
  total: number
  /** 短剧信息列表 */
  album_info_list: FetchAlbumInfo[]
}

/** 短剧详情查询响应数据 */
export interface DetailData {
  /** 总数 */
  total: number
  /** 剧集信息列表 */
  episode_info_list: FetchEpisodeInfo[]
}

/** 短剧查询响应数据 */
export interface DramaFetchResponse {
  /** 批量查询数据 */
  batch_data?: BatchData
  /** 单个短剧查询数据 */
  single_data?: SingleData
  /** 短剧详情查询数据 */
  detail_data?: DetailData
}
