import { PaginationRequest } from '@/common/requests/pagination.request'
import {
  IsOptional,
  IsString,
  IsNotEmpty,
  IsInt,
  IsUrl,
  Min,
  Max,
  IsArray,
  ArrayMaxSize,
  ArrayMinSize,
  IsIn,
} from 'class-validator'
import { Transform, Type } from 'class-transformer'

export class DramaCreateRequest {
  // ====================== 基础信息 (必填) ======================

  /** 短剧标题 */
  @IsNotEmpty({ message: '短剧标题不能为空' })
  @IsString({ message: '短剧标题必须是字符串' })
  title: string

  @IsNotEmpty({ message: '年份不能为空' })
  @IsInt({ message: '年份必须是整数' })
  @Min(1900, { message: '年份不能小于1900' })
  @Max(2900, { message: '年份不能超过2900' })
  @Type(() => Number)
  year: number

  /** 推荐语 */
  @IsNotEmpty({ message: '推荐语不能为空' })
  @IsString({ message: '推荐语必须是字符串' })
  recommendation: string

  /** 总集数 */
  @IsNotEmpty({ message: '总集数不能为空' })
  @IsInt({ message: '总集数必须是整数' })
  @Min(1, { message: '总集数不能小于1' })
  @Max(999, { message: '总集数不能超过999' })
  @Type(() => Number)
  seqNum: number

  /** 原价 */
  @IsNotEmpty({ message: '原价不能为空' })
  @Transform(({ value }) => parseFloat(String(value)))
  @Type(() => Number)
  @Min(0.01, { message: '原价不能小于0.01元' })
  @Max(9999.99, { message: '原价不能超过9999.99元' })
  originalPrice: number

  /** 优惠价 */
  @IsNotEmpty({ message: '优惠价不能为空' })
  @Transform(({ value }) => parseFloat(String(value)))
  @Type(() => Number)
  @Min(0.01, { message: '优惠价不能小于0.01元' })
  @Max(9999.99, { message: '优惠价不能超过9999.99元' })
  discountPrice: number

  /** 分类ID 只有一个 */
  @IsNotEmpty({ message: '请选择分类' })
  @IsString({ message: '分类ID必须是字符串' })
  categoryId: string

  /** 演员 */
  @IsNotEmpty({ message: '演员不能为空' })
  @IsString({ message: '演员必须是字符串' })
  actor: string

  /** 导演 */
  @IsNotEmpty({ message: '导演不能为空' })
  @IsString({ message: '导演必须是字符串' })
  director: string

  /** 题材标签，多个用逗号分隔 */
  @IsNotEmpty({ message: '请选择题材标签' })
  @IsArray({ message: '题材标签必须是数组' })
  @IsInt({ each: true, message: '每个标签必须是数字' })
  @ArrayMinSize(1, { message: '至少选择1个标签' })
  @ArrayMaxSize(3, { message: '标签数量不能超过3个' })
  tagList: number[]

  /** 短剧更新状态: 1-未上映, 2-更新中, 3-已完结 */
  @IsNotEmpty({ message: '短剧状态不能为空' })
  @IsInt({ message: '短剧状态必须是整数' })
  @IsIn([1, 2, 3], { message: '短剧状态必须是1(未上映)、2(更新中)或3(已完结)' })
  @Type(() => Number)
  albumStatus: number

  /** 短剧简介 */
  @IsNotEmpty({ message: '短剧简介不能为空' })
  @IsString({ message: '短剧简介必须是字符串' })
  desp: string

  /** 竖版封面图片URL */
  @IsNotEmpty({ message: '竖版封面图片不能为空' })
  @IsString({ message: '竖版封面图片URL必须是字符串' })
  @IsUrl({}, { message: '竖版封面图片URL格式不正确' })
  coverVertical: string

  /** 横版封面图片URL */
  @IsNotEmpty({ message: '横版封面图片不能为空' })
  @IsString({ message: '横版封面图片URL必须是字符串' })
  @IsUrl({}, { message: '横版封面图片URL格式不正确' })
  coverHorizontal: string

  // ====================== 备案审核信息 (必填) ======================

  /** 平均单集时长，单位分钟 */
  @IsNotEmpty({ message: '平均单集时长不能为空' })
  @IsInt({ message: '平均单集时长必须是整数' })
  @Min(1, { message: '平均单集时长不能小于1分钟' })
  @Max(120, { message: '平均单集时长不能超过120分钟' })
  @Type(() => Number)
  duration: number

  /** 制作机构 */
  @IsNotEmpty({ message: '制作机构不能为空' })
  @IsString({ message: '制作机构必须是字符串' })
  productionOrganisation: string

  /** 制片人 */
  @IsNotEmpty({ message: '制片人不能为空' })
  @IsString({ message: '制片人必须是字符串' })
  producer: string

  /** 内容梗概 */
  @IsNotEmpty({ message: '内容梗概不能为空' })
  @IsString({ message: '内容梗概必须是字符串' })
  summary: string

  /** 成本分配URI */
  @IsNotEmpty({ message: '成本分配URI不能为空' })
  @IsString({ message: '成本分配URI必须是字符串' })
  @IsUrl({}, { message: '成本分配URI格式不正确' })
  costDistributionUri: string

  /** 制作成本类型: 10-30万以下, 20-30～100万, 30-100万以上 */
  @IsNotEmpty({ message: '制作成本类型不能为空' })
  @IsInt({ message: '制作成本类型必须是整数' })
  @IsIn([10, 20, 30], { message: '制作成本类型必须是10(30万以下)、20(30～100万)或30(100万以上)' })
  @Type(() => Number)
  playletProductionCost: number

  // ====================== 可选字段 ======================

  /** 编剧 */
  @IsOptional()
  @IsString({ message: '编剧必须是字符串' })
  screenWriter?: string

  /** 承诺书URI */
  @IsOptional()
  @IsString({ message: '承诺书URI必须是字符串' })
  @IsUrl({}, { message: '承诺书URI格式不正确' })
  assuranceUri?: string

  /** 备案类型: 10-普通备案, 20-重点备案 */
  @IsOptional()
  @IsInt({ message: '备案类型必须是整数' })
  @IsIn([10, 20], { message: '备案类型必须是10(普通备案)或20(重点备案)' })
  @Type(() => Number)
  recordType?: number

  /** 广电备案号 */
  @IsOptional()
  @IsString({ message: '广电备案号必须是字符串' })
  broadcastRecordNumber?: string

  /** 许可证号 */
  @IsOptional()
  @IsString({ message: '许可证号必须是字符串' })
  licenseNum?: string

  /** 登记号 */
  @IsOptional()
  @IsString({ message: '登记号必须是字符串' })
  registrationNum?: string

  /** 普通备案号 */
  @IsOptional()
  @IsString({ message: '普通备案号必须是字符串' })
  ordinaryRecordNum?: string

  /** 重点备案号 */
  @IsOptional()
  @IsString({ message: '重点备案号必须是字符串' })
  keyRecordNum?: string

  /** 剧集介绍图片 */
  @IsOptional()
  @IsArray({ message: '剧集介绍图片必须是数组' })
  @IsString({ each: true, message: '每个介绍图片URL必须是字符串' })
  @IsUrl({}, { each: true, message: '每个介绍图片URL格式不正确' })
  introImages?: string[]

  /** 是否免费试看: 0-否, 1-是 */
  @IsOptional()
  @IsInt({ message: '是否免费试看必须是整数' })
  @IsIn([0, 1], { message: '是否免费试看必须是0(否)或1(是)' })
  @Type(() => Number)
  freePreview?: number

  /** 货币单位 */
  @IsOptional()
  @IsString({ message: '货币单位必须是字符串' })
  @IsIn(['CNY', 'USD', 'EUR'], { message: '货币单位必须是CNY、USD或EUR' })
  currency?: string

  // ====================== 可选字段 ======================

  /** 作者/创作者ID */
  authorId?: string
}

export class DramaUpdateRequest extends DramaCreateRequest {}

export class DramaFilterRequest extends PaginationRequest {
  @IsOptional()
  @IsString()
  title?: string
}
