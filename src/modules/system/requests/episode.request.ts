import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsNumber, IsOptional, Min, Max } from 'class-validator'

export class CreateEpisodeRequest {
  @ApiProperty({ description: '分集标题', example: '第1集：初遇' })
  @IsNotEmpty({ message: '分集标题不能为空' })
  @IsString({ message: '分集标题必须是字符串' })
  title: string

  @ApiProperty({ description: '第几集', example: 1 })
  @IsNotEmpty({ message: '集数不能为空' })
  @IsNumber({}, { message: '集数必须是数字' })
  @Min(1, { message: '集数不能小于1' })
  @Max(999, { message: '集数不能大于999' })
  seq: number

  @ApiProperty({ description: '分集封面图片URL', example: 'https://example.com/cover.jpg' })
  @IsNotEmpty({ message: '封面图片URL不能为空' })
  @IsString({ message: '封面图片URL必须是字符串' })
  coverUrl: string

  @ApiProperty({ description: '抖音封面图片ID', example: '7532905646191018523' })
  @IsNotEmpty({ message: '抖音封面图片ID不能为空' })
  @IsString({ message: '抖音封面图片ID必须是字符串' })
  coverOpenPicId: string

  @ApiProperty({ description: '视频URL', example: 'https://example.com/video.mp4' })
  @IsNotEmpty({ message: '视频URL不能为空' })
  @IsString({ message: '视频URL必须是字符串' })
  videoUrl: string

  @ApiProperty({ description: '抖音视频ID', example: 'v0300fg10000ck9a...' })
  @IsNotEmpty({ message: '抖音视频ID不能为空' })
  @IsString({ message: '抖音视频ID必须是字符串' })
  openVideoId: string

  @ApiProperty({ description: '分集简介', required: false, example: '主角初次登场...' })
  @IsOptional()
  @IsString({ message: '分集简介必须是字符串' })
  description?: string

  @ApiProperty({ description: '分集时长', required: false, example: '15:30' })
  @IsOptional()
  @IsString({ message: '分集时长必须是字符串' })
  duration?: string

  @ApiProperty({ description: '文件大小', required: false, example: '100MB' })
  @IsOptional()
  @IsString({ message: '文件大小必须是字符串' })
  fileSize?: string

  @ApiProperty({ description: '是否免费', required: false, example: 0, enum: [0, 1] })
  @IsOptional()
  @IsNumber({}, { message: '是否免费必须是数字' })
  @Min(0, { message: '是否免费值无效' })
  @Max(1, { message: '是否免费值无效' })
  isFree?: number

  @ApiProperty({ description: '价格', required: false, example: 1.99 })
  @IsOptional()
  @IsNumber({}, { message: '价格必须是数字' })
  @Min(0, { message: '价格不能小于0' })
  price?: number
}
