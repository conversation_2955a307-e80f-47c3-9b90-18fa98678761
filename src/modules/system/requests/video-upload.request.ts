import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsOptional, IsUrl } from 'class-validator'

export class VideoUploadRequest {
  @ApiProperty({ description: '视频URL', example: 'https://example.com/video.mp4' })
  @IsNotEmpty({ message: '视频URL不能为空' })
  @IsString({ message: '视频URL必须是字符串' })
  @IsUrl({}, { message: '视频URL格式不正确' })
  videoUrl: string

  @ApiProperty({ description: '视频标题', example: '第1集：初遇' })
  @IsNotEmpty({ message: '视频标题不能为空' })
  @IsString({ message: '视频标题必须是字符串' })
  title: string

  @ApiProperty({ description: '视频描述', required: false, example: '主角初次登场...' })
  @IsOptional()
  @IsString({ message: '视频描述必须是字符串' })
  description?: string
}


